{"name": "propbolt-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "14.0.4", "react": "^18", "react-dom": "^18", "typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "tailwindcss": "^3.3.0", "autoprefixer": "^10.0.1", "postcss": "^8", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "react-leaflet": "^4.2.1", "leaflet": "^1.9.4", "@types/leaflet": "^1.9.8"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.0.4"}}