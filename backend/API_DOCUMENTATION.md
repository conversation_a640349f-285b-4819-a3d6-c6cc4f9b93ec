# PropBolt API Documentation

## Overview

PropBolt is a real estate data API that provides property details, rent estimates, and property search functionality. The API scrapes and processes data from Zillow to provide comprehensive property information.

## Base URL
```
http://localhost:8080
```

## Authentication

The API uses RapidAPI proxy secret validation for production environments. For local development, this can be disabled using environment variables:
- `DISABLE_RAPIDAPI_PROXY_VALIDATION=true`
- `DISABLE_FORCE_SSL=true`

## Endpoints

### 1. Health Check
**GET** `/`

Returns the health status of the API.

**Response:**
```json
{
  "status": "ok"
}
```

### 2. Property Details (Full)
**GET** `/property`

Retrieves comprehensive property information by ID, URL, or address.

**Parameters:**
- `id` (optional): Zillow Property ID (zpid)
- `url` (optional): Zillow property URL
- `address` (optional): Property address
- `listingPhotos` (optional): Include listing photos (`true`/`false`, default: `false`)

**Example Requests:**
```bash
# By Property ID
GET /property?id=*********

# By Property URL
GET /property?url=https://www.zillow.com/homedetails/123-main-st/*********_zpid/

# By Address
GET /property?address=123 Main St, City, State

# With listing photos
GET /property?id=*********&listingPhotos=true
```

**Response Structure:**
```json
{
  "homeStatus": ["FOR_SALE"],
  "address": {
    "streetAddress": ["123 Main St"],
    "city": ["City"],
    "state": ["State"],
    "zipcode": ["12345"]
  },
  "bedrooms": 3,
  "bathrooms": 2.5,
  "yearBuilt": 2020,
  "zestimate": 450000,
  "price": 475000,
  "zpid": *********,
  "latitude": 40.7128,
  "longitude": -74.0060,
  "rentZestimate": 2500,
  "daysOnZillow": 15,
  "lotSize": 7200,
  "livingArea": 2000,
  "propertyTaxRate": 0.012,
  "monthlyHoaFee": 150,
  "description": ["Beautiful home with modern amenities"],
  "responsivePhotos": [
    {
      "url": "https://photos.zillowstatic.com/...",
      "width": 1024,
      "height": 768
    }
  ],
  "resoFacts": {
    "bedrooms": 3,
    "bathrooms": 2.5,
    "livingArea": ["2000"],
    "lotSize": ["7200"],
    "yearBuilt": 2020,
    "homeType": ["SINGLE_FAMILY"],
    "heating": ["Central"],
    "cooling": ["Central Air"],
    "parking": ["2 Car Garage"],
    "appliances": ["Dishwasher", "Refrigerator"]
  },
  "schools": [
    {
      "name": ["Elementary School"],
      "rating": 8,
      "distance": 0.5,
      "level": ["Elementary"],
      "type": ["Public"]
    }
  ],
  "priceHistory": [
    {
      "date": ["2023-01-15"],
      "price": 475000,
      "event": ["Listed for sale"]
    }
  ],
  "taxHistory": [
    {
      "time": 1640995200000,
      "taxPaid": 5400,
      "value": 450000
    }
  ]
}
```

### 3. Property Details (Minimal)
**GET** `/propertyMinimal`

Retrieves essential property information with reduced data payload.

**Parameters:**
- `id` (optional): Zillow Property ID (zpid)
- `url` (optional): Zillow property URL  
- `address` (optional): Property address
- `listingPhotos` (optional): Include listing photos (`true`/`false`, default: `false`)

**Response Structure:**
```json
{
  "address": {
    "streetAddress": ["123 Main St"],
    "city": ["City"],
    "state": ["State"],
    "zipcode": ["12345"]
  },
  "homeStatus": ["FOR_SALE"],
  "zestimate": 450000,
  "bedrooms": 3,
  "bathrooms": 2.5,
  "livingArea": ["2000"],
  "zpid": *********,
  "price": 475000,
  "yearBuilt": 2020,
  "daysOnZillow": 15,
  "mlsId": ["MLS123456"],
  "propertyZillowURL": "https://www.zillow.com/homedetails/*********_zpid/",
  "responsivePhotos": []
}
```

### 4. Property Images Only
**GET** `/propertyImages`

Retrieves only the property images and basic identification information.

**Parameters:**
- `id` (optional): Zillow Property ID (zpid)
- `url` (optional): Zillow property URL
- `address` (optional): Property address

**Response Structure:**
```json
{
  "address": {
    "streetAddress": ["123 Main St"],
    "city": ["City"],
    "state": ["State"],
    "zipcode": ["12345"]
  },
  "zpid": *********,
  "responsivePhotos": [
    {
      "url": "https://photos.zillowstatic.com/...",
      "width": 1024,
      "height": 768
    }
  ]
}
```

### 5. Rent Estimate
**GET** `/rentEstimate`

Retrieves rental market analysis and rent estimates for a given address.

**Parameters:**
- `address` (required): Property address
- `compPropStatus` (optional): Include comparable properties status (`true`/`false`)
- `distanceInMiles` (optional): Search radius for comparables (default: 5.0)

**Example Request:**
```bash
GET /rentEstimate?address=123 Main St, City, State&compPropStatus=true&distanceInMiles=3.0
```

**Response Structure:**
```json
{
  "data": {
    "byAddress": {
      "geo": {
        "lat": 40.7128,
        "lon": -74.0060
      },
      "floorplans": [
        {
          "zpid": "*********",
          "numBeds": 3,
          "numFullBaths": 2,
          "zestimate": {
            "rentZestimate": 2500,
            "rentZestimateRangeLow": 2300,
            "rentZestimateRangeHigh": 2700
          },
          "minSqft": 1800,
          "maxSqft": 2200,
          "address": {
            "street": "123 Main St",
            "city": "City",
            "state": "State",
            "zip": "12345",
            "unit": ""
          }
        }
      ],
      "marketSummary": {
        "url": "https://www.zillow.com/rental-manager/...",
        "areaName": "City, State",
        "beds": [
          {
            "summary": {
              "medianRent": 2400,
              "monthlyChange": 0.02,
              "yearlyChange": 0.08,
              "avgDaysOnMarket": 25,
              "availableRentals": 45
            }
          }
        ]
      },
      "similarFloorplans": {
        "results": [
          {
            "zpid": "987654321",
            "numBeds": 3,
            "numFullBaths": 2,
            "rentZestimate": 2450,
            "address": {
              "street": "456 Oak Ave",
              "city": "City",
              "state": "State",
              "zip": "12345"
            },
            "geo": {
              "lat": 40.7130,
              "lon": -74.0058
            },
            "amenities": {
              "laundry": {
                "inUnit": true,
                "shared": false
              },
              "parking": {
                "garage": true,
                "offStreet": true
              },
              "hvac": {
                "airConditioning": true,
                "heating": true
              },
              "pets": {
                "allowed": true,
                "deposit": 500
              }
            }
          }
        ]
      }
    }
  }
}
```

### 6. Property Search - For Sale
**GET** `/search/for-sale`

Search for properties currently for sale within specified geographic bounds.

**Required Parameters:**
- `neLat` (required): Northeast latitude boundary
- `neLong` (required): Northeast longitude boundary
- `swLat` (required): Southwest latitude boundary
- `swLong` (required): Southwest longitude boundary

**Optional Parameters:**
- `page` (optional): Page number for pagination (default: 1)
- `zoom` (optional): Map zoom level 1-20 (default: 10)

**Property Type Filters:**
- `isAllHomes` (optional): Include all home types (default: true)
- `isTownhouse` (optional): Include townhouses (default: false)
- `isMultiFamily` (optional): Include multi-family properties (default: false)
- `isCondo` (optional): Include condos (default: false)
- `isLotLand` (optional): Include lots/land (default: false)
- `isApartment` (optional): Include apartments (default: false)
- `isManufactured` (optional): Include manufactured homes (default: false)
- `isApartmentOrCondo` (optional): Include apartments or condos (default: false)

**School Filters:**
- `isElementarySchool` (optional): Filter by elementary school (default: false)
- `isMiddleSchool` (optional): Filter by middle school (default: false)
- `isHighSchool` (optional): Filter by high school (default: false)
- `isPublicSchool` (optional): Filter by public schools (default: false)
- `isPrivateSchool` (optional): Filter by private schools (default: false)
- `isCharterSchool` (optional): Filter by charter schools (default: false)
- `includeUnratedSchools` (optional): Include unrated schools (default: false)

**Price Filters:**
- `priceMin` (optional): Minimum price (default: 0)
- `priceMax` (optional): Maximum price (default: 0)
- `monthlyPaymentMin` (optional): Minimum monthly payment (default: 0)
- `monthlyPaymentMax` (optional): Maximum monthly payment (default: 0)

**Example Request:**
```bash
GET /search/for-sale?neLat=37.8&neLong=-122.3&swLat=37.7&swLong=-122.5&zoom=12&page=1&isCondo=true&priceMin=500000&priceMax=1000000
```

### 7. Property Search - For Rent
**GET** `/search/for-rent`

Search for rental properties within specified geographic bounds.

**Required Parameters:**
- `neLat` (required): Northeast latitude boundary
- `neLong` (required): Northeast longitude boundary
- `swLat` (required): Southwest latitude boundary
- `swLong` (required): Southwest longitude boundary

**Optional Parameters:**
- `page` (optional): Page number for pagination (default: 1)
- `zoom` (optional): Map zoom level 1-20 (default: 10)

**Example Request:**
```bash
GET /search/for-rent?neLat=37.8&neLong=-122.3&swLat=37.7&swLong=-122.5&zoom=12&page=1
```

### 8. Property Search - Recently Sold
**GET** `/search/sold`

Search for recently sold properties within specified geographic bounds.

**Required Parameters:**
- `neLat` (required): Northeast latitude boundary
- `neLong` (required): Northeast longitude boundary
- `swLat` (required): Southwest latitude boundary
- `swLong` (required): Southwest longitude boundary

**Optional Parameters:**
- `page` (optional): Page number for pagination (default: 1)
- `zoom` (optional): Map zoom level 1-20 (default: 10)

**Example Request:**
```bash
GET /search/sold?neLat=37.8&neLong=-122.3&swLat=37.7&swLong=-122.5&zoom=12&page=1
```

### Search Response Structure
All search endpoints return the same response format:

```json
{
  "listResults": [
    {
      "zpid": "*********",
      "id": "*********",
      "hasImage": true,
      "imgSrc": "https://photos.zillowstatic.com/...",
      "statusText": "For sale",
      "address": "123 Main St, City, State 12345",
      "addressStreet": "123 Main St",
      "addressCity": "City",
      "addressState": "State",
      "addressZipcode": "12345",
      "area": 2000,
      "statusType": "FOR_SALE",
      "detailUrl": "/homedetails/123-main-st/*********_zpid/",
      "price": "$475,000",
      "unformattedPrice": 475000,
      "latLong": {
        "latitude": 40.7128,
        "longitude": -74.0060
      },
      "hdpData": {
        "homeInfo": {
          "zpid": *********,
          "streetAddress": "123 Main St",
          "city": "City",
          "state": "State",
          "zipcode": "12345",
          "price": 475000,
          "bathrooms": 2.5,
          "bedrooms": 3,
          "livingArea": 2000,
          "homeType": "SINGLE_FAMILY",
          "homeStatus": "FOR_SALE",
          "daysOnZillow": 15,
          "zestimate": 450000
        }
      },
      "carouselPhotos": [
        {
          "url": "https://photos.zillowstatic.com/..."
        }
      ]
    }
  ],
  "mapResults": [
    {
      "zpid": "*********",
      "address": "123 Main St, City, State 12345",
      "price": "$475,000",
      "beds": 3,
      "baths": 2.5,
      "area": 2000,
      "latLong": {
        "latitude": 40.7128,
        "longitude": -74.0060
      },
      "statusType": "FOR_SALE",
      "statusText": "For sale"
    }
  ],
  "totalCount": 1
}
```

## Static Files

The API also serves static files for documentation and tutorials:

- **GET** `/favicon.ico` - Favicon
- **GET** `/api-logo.png` - API logo
- **GET** `/cover-photo-tutorial-one.png` - Tutorial cover image
- **GET** `/byte-media-logo-v2.png` - Byte Media logo
- **GET** `/rapid/tutorial-one-code` - Tutorial HTML page
- **GET** `/rapid/curl` - cURL examples HTML page
- **GET** `/public/*` - Static files directory

## Data Types

### StringOrArray
Many fields in the API can return either a string or an array of strings. This flexible type handles various data formats from the source.

### Address Object
```json
{
  "streetAddress": ["123 Main St"],
  "city": ["City"],
  "state": ["State"],
  "zipcode": ["12345"]
}
```

### ResponsivePhotos Array
```json
[
  {
    "url": "https://photos.zillowstatic.com/...",
    "width": 1024,
    "height": 768
  }
]
```

## Error Responses

The API returns appropriate HTTP status codes and error messages:

- **400 Bad Request**: Missing required parameters or invalid parameter values
- **403 Forbidden**: Invalid or missing RapidAPI proxy secret (production only)
- **500 Internal Server Error**: Server-side processing errors

**Error Response Format:**
```json
{
  "error": "Error message describing the issue"
}
```

## Rate Limiting

The API implements standard rate limiting practices. Excessive requests may be throttled or blocked.

## Environment Variables

- `PORT`: Server port (required)
- `RAPIDAPI_PROXY_SECRET`: RapidAPI proxy validation secret (production)
- `DISABLE_RAPIDAPI_PROXY_VALIDATION`: Disable proxy validation (development)
- `DISABLE_FORCE_SSL`: Disable SSL enforcement (development)

## Usage Examples

### Get Property Details by Address
```bash
curl "http://localhost:8080/property?address=1600 Amphitheatre Parkway, Mountain View, CA"
```

### Get Minimal Property Info with Photos
```bash
curl "http://localhost:8080/propertyMinimal?id=*********&listingPhotos=true"
```

### Get Rent Estimate
```bash
curl "http://localhost:8080/rentEstimate?address=123 Main St, San Francisco, CA&distanceInMiles=2.0"
```

### Get Only Property Images
```bash
curl "http://localhost:8080/propertyImages?url=https://www.zillow.com/homedetails/123-main-st/*********_zpid/"
```

### Search Properties For Sale
```bash
curl "http://localhost:8080/search/for-sale?neLat=37.8&neLong=-122.3&swLat=37.7&swLong=-122.5&zoom=12&page=1&isCondo=true&priceMin=500000&priceMax=1000000"
```

### Search Rental Properties
```bash
curl "http://localhost:8080/search/for-rent?neLat=37.8&neLong=-122.3&swLat=37.7&swLong=-122.5&zoom=12"
```

### Search Recently Sold Properties
```bash
curl "http://localhost:8080/search/sold?neLat=37.8&neLong=-122.3&swLat=37.7&swLong=-122.5&zoom=12"
```

## Search Functionality (Internal)

The API includes internal search functionality through the `search` package that supports:

### Search Types
- **For Sale Properties**: Search properties currently for sale
- **For Rent Properties**: Search rental properties
- **Recently Sold Properties**: Search recently sold properties

### Search Parameters
- **Geographic Bounds**: Northeast and Southwest coordinates
- **Zoom Level**: Map zoom level (1-20)
- **Pagination**: Page number for results
- **Property Filters**:
  - Home types (townhouse, multi-family, condo, lot/land, apartment, manufactured)
  - School filters (elementary, middle, high school types)
  - Price ranges (min/max price, monthly payment)

### Search Response Structure
Returns both list results and map results with detailed property information including:
- Property IDs and basic info
- Pricing and status
- Geographic coordinates
- Property images
- Detailed home information

## Data Models

### PropertyInfo (Full Details)
The complete property information structure includes:
- **Basic Info**: Address, bedrooms, bathrooms, year built
- **Financial**: Price, Zestimate, rent estimate, property taxes, HOA fees
- **Physical**: Lot size, living area, property type
- **Market Data**: Days on Zillow, price history, tax history
- **Location**: Latitude, longitude, nearby schools
- **Media**: Responsive photos, virtual tour URLs
- **Detailed Facts**: RESO facts with comprehensive property details

### PropertyMinimalInfo
Streamlined version containing essential information:
- Address and basic property details
- Pricing and estimates
- Key metrics (bedrooms, bathrooms, living area)
- Property URL and identification

### ImagesOnly
Minimal structure for image-focused requests:
- Property identification (address, zpid)
- Complete responsive photos array

### RentZestimateResponse
Comprehensive rental market analysis:
- **Geographic Data**: Property coordinates
- **Floorplans**: Available unit configurations with rent estimates
- **Market Summary**: Area rental statistics and trends
- **Comparable Properties**: Similar properties with amenities and pricing

## Technical Implementation

### Architecture
- **Go HTTP Server**: Built with standard `net/http` package
- **Modular Design**: Separate packages for different functionalities
  - `details`: Property detail retrieval and parsing
  - `zestimate`: Rent estimation functionality
  - `search`: Property search capabilities
  - `utils`: Utility functions
  - `propbolthelper`: Client helper functions

### Data Processing
- **Web Scraping**: Extracts data from Zillow property pages
- **JSON Parsing**: Processes complex nested JSON structures
- **Data Transformation**: Converts raw data to structured API responses
- **Flexible Types**: Handles varying data formats with custom unmarshaling

### Error Handling
- Comprehensive error checking at each processing stage
- Graceful handling of missing or malformed data
- Detailed error messages for debugging

### Performance Considerations
- HTTP client with connection pooling
- Configurable timeouts and retry logic
- Efficient JSON processing
- Minimal memory footprint for large datasets

## Development and Deployment

### Local Development
```bash
# Build the application
go build -o propbolt

# Run locally with development settings
DISABLE_RAPIDAPI_PROXY_VALIDATION=true DISABLE_FORCE_SSL=true PORT=8080 ./propbolt
```

### Production Deployment
```bash
# Build for Linux production
GOOS=linux GOARCH=amd64 go build -o propbolt

# Set required environment variables
export PORT=8080
export RAPIDAPI_PROXY_SECRET=your_secret_here

# Run the application
./propbolt
```

### Dependencies
- `github.com/PuerkitoBio/goquery v1.9.2`: HTML parsing and DOM manipulation
- `github.com/andybalholm/cascadia v1.3.2`: CSS selector engine
- `golang.org/x/net v0.26.0`: Extended networking libraries

## API Limitations and Considerations

### Rate Limiting
- Requests are subject to rate limiting to prevent abuse
- Excessive requests may result in temporary blocking

### Data Freshness
- Property data is scraped in real-time from Zillow
- Information accuracy depends on source data availability
- Some properties may have limited or outdated information

### Geographic Coverage
- Primarily covers properties listed on Zillow
- Coverage varies by region and property type
- International properties may have limited support

### Legal Considerations
- API is for informational purposes only
- Users should verify property information independently
- Compliance with data usage policies is required

## Support and Contact

For technical support or questions about the API:
- Email: <EMAIL>
- Review the static documentation pages available through the API endpoints

## Version Information

Current API version corresponds to commit `d145dfc` (june26v1.0) of the PropBolt repository.
