package main

import (
    "encoding/json"
    "fmt"
    "log"
    "net/http"
    "os"
    "path/filepath"
    "strconv"
    "propbolt/zestimate"
    "propbolt/details"
    "propbolt/search"
)

/* internal notess
lsof -i :8080                                    
kill -9
git add .
git commit -m ""
git push origin main
Production: GOOS=linux GOARCH=amd64 go build -o propbolt
Local: go build -o propbolt
Local: DISABLE_RAPIDAPI_PROXY_VALIDATION=true PORT=8080 ./propbolt
Local: DISABLE_RAPIDAPI_PROXY_VALIDATION=true DISABLE_FORCE_SSL=true PORT=8080 ./propbolt
*/

// Middleware to validate RapidAPI proxy secret
func validateRapidAPIProxySecret(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        // Check if validation should be disabled
        if os.Getenv("DISABLE_RAPIDAPI_PROXY_VALIDATION") == "true" {
            next.ServeHTTP(w, r)
            return
        }

        // Exclude certain paths from validation
        if r.URL.Path == "/rapid/curl" || r.URL.Path == "/rapid/tutorial-one-code" || r.URL.Path == "/favicon.ico" || r.URL.Path == "/public" || r.URL.Path == "/" || r.URL.Path == "/api-logo.png" || r.URL.Path == "/cover-photo-tutorial-one.png" || r.URL.Path == "/byte-media-logo-v2.png" {
            next.ServeHTTP(w, r)
            return
        }

        // Load RapidAPI proxy secret from environment variables
        validProxySecret := os.Getenv("RAPIDAPI_PROXY_SECRET")
        if validProxySecret == "" {
            log.Printf("RapidAPI proxy secret is not set")
            http.Error(w, "Forbidden Access. Contact: <EMAIL>", http.StatusForbidden)
            return
        }
        next.ServeHTTP(w, r)
    })
}

// Handler for the health check endpoint
func healthCheckHandler(w http.ResponseWriter, r *http.Request) {
    w.Header().Set("Content-Type", "application/json")
    w.WriteHeader(http.StatusOK)
    json.NewEncoder(w).Encode(map[string]string{"status": "ok"})
}

// Handler for serving static files
func serveStaticFile(filePath string) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        http.ServeFile(w, r, filepath.Join("public", filePath))
    }
}

// Handler for serving the favicon
func faviconHandler(w http.ResponseWriter, r *http.Request) {
    http.ServeFile(w, r, filepath.Join("public", "favicon.ico"))
}

// New handler for the /property endpoint
func propertyHandler(w http.ResponseWriter, r *http.Request) {
    // Parse the listingPhotos parameter
    listingPhotosStr := r.URL.Query().Get("listingPhotos")
    listingPhotos := false
    if listingPhotosStr == "true" {
        listingPhotos = true
    }

    var property details.PropertyInfo
    var err error

    // Check for ID parameter
    propertyIDStr := r.URL.Query().Get("id")
    if propertyIDStr != "" {
        propertyID, err := strconv.ParseInt(propertyIDStr, 10, 64)
        if err != nil {
            http.Error(w, fmt.Sprintf("Error parsing property ID: %v", err), http.StatusBadRequest)
            return
        }

        property, err = details.FromPropertyID(propertyID, nil)
    } else {
        // Check for URL parameter
        propertyURL := r.URL.Query().Get("url")
        if propertyURL != "" {
            property, err = details.FromPropertyURL(propertyURL, nil)
        } else {
            // Check for Address parameter
            homeAddress := r.URL.Query().Get("address")
            if homeAddress != "" {
                property, err = details.FromHomeAddress(homeAddress, nil)
            } else {
                // If no valid parameters are provided, return an error
                http.Error(w, "Must provide ID, URL, or Address parameter", http.StatusBadRequest)
                return
            }
        }
    }

    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving property details: %v", err), http.StatusInternalServerError)
        return
    }

    if !listingPhotos {
        property.ResponsivePhotos = nil
    }

    writeJSONResponse(w, property)
}

func rentZestimateHandler(w http.ResponseWriter, r *http.Request) {
    address := r.URL.Query().Get("address")
    if address == "" {
        http.Error(w, "Address parameter is required", http.StatusBadRequest)
        return
    }

    var compPropStatus *bool
    compPropStatusStr := r.URL.Query().Get("compPropStatus")
    if compPropStatusStr != "" {
        if compPropStatusStr == "true" || compPropStatusStr == "false" {
            val, _ := strconv.ParseBool(compPropStatusStr)
            compPropStatus = &val
        } else {
            http.Error(w, "Invalid compPropStatus parameter", http.StatusBadRequest)
            return
        }
    }

    distanceInMilesStr := r.URL.Query().Get("distanceInMiles")
    var distanceInMiles float64 = 5 // Default value
    if distanceInMilesStr != "" {
        var err error
        distanceInMiles, err = strconv.ParseFloat(distanceInMilesStr, 64)
        if err != nil {
            http.Error(w, "Invalid distanceInMiles parameter", http.StatusBadRequest)
            return
        }
    }

    rentZestimate, err := zestimate.GetRentZestimate(address, compPropStatus, distanceInMiles)
    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving rent zestimate: %v", err), http.StatusInternalServerError)
        return
    }

    writeJSONResponse(w, rentZestimate)
}

func main() {
    mux := http.NewServeMux()

    mux.HandleFunc("/property", propertyHandler) // New unified endpoint
    mux.HandleFunc("/propertyMinimal", propertyMinimalHandler)
    mux.HandleFunc("/propertyImages", propertyImagesHandler)
    mux.HandleFunc("/rentEstimate", rentZestimateHandler) // New endpoint for property images

    // Search endpoints
    mux.HandleFunc("/search/for-sale", searchForSaleHandler)
    mux.HandleFunc("/search/for-rent", searchForRentHandler)
    mux.HandleFunc("/search/sold", searchSoldHandler)

    // Health Check Endpoint
    mux.HandleFunc("/", healthCheckHandler)

    // Favicon Endpoint
    mux.HandleFunc("/favicon.ico", serveStaticFile("favicon.ico"))

    // Additional static file routes
    mux.HandleFunc("/api-logo.png", serveStaticFile("api-logo.png"))
    mux.HandleFunc("/cover-photo-tutorial-one.png", serveStaticFile("cover-photo-tutorial-one.png"))
    mux.HandleFunc("/rapid/tutorial-one-code", serveStaticFile("tutorials-one.html"))
    mux.HandleFunc("/byte-media-logo-v2.png", serveStaticFile("byte-media-logo-v2.png"))
    mux.HandleFunc("/rapid/curl", serveStaticFile("curl_input.html"))

    // Serve static files from the 'public' directory without authentication
    mux.Handle("/public/", http.StripPrefix("/public/", http.FileServer(http.Dir("public"))))

    // Wrap the handlers with the RapidAPI proxy secret validation middleware
    handler := validateRapidAPIProxySecret(mux)

    port := os.Getenv("PORT")
    if port == "" {
        log.Fatal("PORT environment variable is not set")
    }

    fmt.Printf("Server started at port %s\n", port)
    log.Fatal(http.ListenAndServe(":"+port, handler))
}

func propertyMinimalHandler(w http.ResponseWriter, r *http.Request) {
    // Parse the listingPhotos parameter
    listingPhotosStr := r.URL.Query().Get("listingPhotos")
    listingPhotos := false
    if listingPhotosStr == "true" {
        listingPhotos = true
    }

    propertyIDStr := r.URL.Query().Get("id")
    propertyURL := r.URL.Query().Get("url")
    homeAddress := r.URL.Query().Get("address")

    var property details.PropertyMinimalInfo
    var err error

    if propertyIDStr != "" {
        propertyID, err := strconv.ParseInt(propertyIDStr, 10, 64)
        if err != nil {
            http.Error(w, fmt.Sprintf("Error parsing property ID: %v", err), http.StatusBadRequest)
            return
        }
        property, err = details.FromPropertyIDMinimal(propertyID, nil)
    } else if propertyURL != "" {
        property, err = details.FromPropertyURLMinimal(propertyURL, nil)
    } else if homeAddress != "" {
        property, err = details.FromHomeAddressMinimal(homeAddress, nil)
    } else {
        http.Error(w, "Either Property ID, URL, or Address is required", http.StatusBadRequest)
        return
    }

    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving property details: %v", err), http.StatusInternalServerError)
        return
    }

    if !listingPhotos {
        property.ResponsivePhotos = nil
    }

    writeJSONResponse(w, property)
}

func propertyImagesHandler(w http.ResponseWriter, r *http.Request) {
    propertyIDStr := r.URL.Query().Get("id")
    propertyURL := r.URL.Query().Get("url")
    homeAddress := r.URL.Query().Get("address")

    var property details.ImagesOnly
    var err error

    switch {
    case propertyIDStr != "":
        propertyID, err := strconv.ParseInt(propertyIDStr, 10, 64)
        if err != nil {
            http.Error(w, fmt.Sprintf("Error parsing property ID: %v", err), http.StatusBadRequest)
            return
        }
        property, err = details.FromPropertyIDPhotos(propertyID, nil)
    case propertyURL != "":
        property, err = details.FromPropertyURLPhotos(propertyURL, nil)
    case homeAddress != "":
        property, err = details.FromHomeAddressPhotos(homeAddress, nil)
    default:
        http.Error(w, "Must provide ID, URL, or Address parameter", http.StatusBadRequest)
        return
    }

    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving property details: %v", err), http.StatusInternalServerError)
        return
    }

    writeJSONResponse(w, property)
}


func writeJSONResponse(w http.ResponseWriter, data interface{}) {
    w.Header().Set("Content-Type", "application/json")
    rawJSON, err := json.MarshalIndent(data, "", "  ")
    if err != nil {
        http.Error(w, fmt.Sprintf("Error marshalling property details: %v", err), http.StatusInternalServerError)
        return
    }
    w.Write(rawJSON)
}

// Helper function to parse boolean parameters
func parseBoolParam(r *http.Request, param string, defaultValue bool) bool {
    value := r.URL.Query().Get(param)
    if value == "" {
        return defaultValue
    }
    result, err := strconv.ParseBool(value)
    if err != nil {
        return defaultValue
    }
    return result
}

// Helper function to parse integer parameters
func parseIntParam(r *http.Request, param string, defaultValue int) (int, error) {
    value := r.URL.Query().Get(param)
    if value == "" {
        return defaultValue, nil
    }
    result, err := strconv.Atoi(value)
    if err != nil {
        return defaultValue, fmt.Errorf("invalid %s parameter: %v", param, err)
    }
    return result, nil
}

// Helper function to parse float parameters
func parseFloatParam(r *http.Request, param string, defaultValue float64) (float64, error) {
    value := r.URL.Query().Get(param)
    if value == "" {
        return defaultValue, nil
    }
    result, err := strconv.ParseFloat(value, 64)
    if err != nil {
        return defaultValue, fmt.Errorf("invalid %s parameter: %v", param, err)
    }
    return result, nil
}

// Search response structure
type SearchResponse struct {
    ListResults []search.ListResult `json:"listResults"`
    MapResults  []search.MapResult  `json:"mapResults"`
    TotalCount  int                 `json:"totalCount"`
}

// Handler for /search/for-sale endpoint
func searchForSaleHandler(w http.ResponseWriter, r *http.Request) {
    // Required geographic bounds
    neLat, err := parseFloatParam(r, "neLat", 0)
    if err != nil || neLat == 0 {
        http.Error(w, "neLat parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    neLong, err := parseFloatParam(r, "neLong", 0)
    if err != nil || neLong == 0 {
        http.Error(w, "neLong parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    swLat, err := parseFloatParam(r, "swLat", 0)
    if err != nil || swLat == 0 {
        http.Error(w, "swLat parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    swLong, err := parseFloatParam(r, "swLong", 0)
    if err != nil || swLong == 0 {
        http.Error(w, "swLong parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    // Optional parameters with defaults
    pagination, err := parseIntParam(r, "page", 1)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    zoomValue, err := parseIntParam(r, "zoom", 10)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }
    if zoomValue < 1 || zoomValue > 20 {
        http.Error(w, "zoom parameter must be between 1 and 20", http.StatusBadRequest)
        return
    }

    // Property type filters
    isAllHomes := parseBoolParam(r, "isAllHomes", true)
    isTownhouse := parseBoolParam(r, "isTownhouse", false)
    isMultiFamily := parseBoolParam(r, "isMultiFamily", false)
    isCondo := parseBoolParam(r, "isCondo", false)
    isLotLand := parseBoolParam(r, "isLotLand", false)
    isApartment := parseBoolParam(r, "isApartment", false)
    isManufactured := parseBoolParam(r, "isManufactured", false)
    isApartmentOrCondo := parseBoolParam(r, "isApartmentOrCondo", false)

    // School filters
    isElementarySchool := parseBoolParam(r, "isElementarySchool", false)
    isMiddleSchool := parseBoolParam(r, "isMiddleSchool", false)
    isHighSchool := parseBoolParam(r, "isHighSchool", false)
    isPublicSchool := parseBoolParam(r, "isPublicSchool", false)
    isPrivateSchool := parseBoolParam(r, "isPrivateSchool", false)
    isCharterSchool := parseBoolParam(r, "isCharterSchool", false)
    includeUnratedSchools := parseBoolParam(r, "includeUnratedSchools", false)

    // Price filters
    priceMin, err := parseIntParam(r, "priceMin", 0)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    priceMax, err := parseIntParam(r, "priceMax", 0)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    monthlyPaymentMin, err := parseIntParam(r, "monthlyPaymentMin", 0)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    monthlyPaymentMax, err := parseIntParam(r, "monthlyPaymentMax", 0)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    // Call the search function
    listResults, mapResults, err := search.ForSale(
        pagination, zoomValue, neLat, neLong, swLat, swLong,
        isAllHomes, isElementarySchool, isMiddleSchool, isHighSchool,
        isPublicSchool, isPrivateSchool, isCharterSchool, includeUnratedSchools,
        isTownhouse, isMultiFamily, isCondo, isLotLand, isApartment,
        isManufactured, isApartmentOrCondo,
        priceMin, priceMax, monthlyPaymentMin, monthlyPaymentMax,
        nil, // proxyURL
    )

    if err != nil {
        http.Error(w, fmt.Sprintf("Error performing search: %v", err), http.StatusInternalServerError)
        return
    }

    response := SearchResponse{
        ListResults: listResults,
        MapResults:  mapResults,
        TotalCount:  len(listResults),
    }

    writeJSONResponse(w, response)
}

// Handler for /search/for-rent endpoint
func searchForRentHandler(w http.ResponseWriter, r *http.Request) {
    // Required geographic bounds
    neLat, err := parseFloatParam(r, "neLat", 0)
    if err != nil || neLat == 0 {
        http.Error(w, "neLat parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    neLong, err := parseFloatParam(r, "neLong", 0)
    if err != nil || neLong == 0 {
        http.Error(w, "neLong parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    swLat, err := parseFloatParam(r, "swLat", 0)
    if err != nil || swLat == 0 {
        http.Error(w, "swLat parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    swLong, err := parseFloatParam(r, "swLong", 0)
    if err != nil || swLong == 0 {
        http.Error(w, "swLong parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    // Optional parameters with defaults
    pagination, err := parseIntParam(r, "page", 1)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    zoomValue, err := parseIntParam(r, "zoom", 10)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }
    if zoomValue < 1 || zoomValue > 20 {
        http.Error(w, "zoom parameter must be between 1 and 20", http.StatusBadRequest)
        return
    }

    // Call the search function
    listResults, mapResults, err := search.ForRent(
        pagination, zoomValue, neLat, neLong, swLat, swLong,
        nil, // proxyURL
    )

    if err != nil {
        http.Error(w, fmt.Sprintf("Error performing search: %v", err), http.StatusInternalServerError)
        return
    }

    response := SearchResponse{
        ListResults: listResults,
        MapResults:  mapResults,
        TotalCount:  len(listResults),
    }

    writeJSONResponse(w, response)
}

// Handler for /search/sold endpoint
func searchSoldHandler(w http.ResponseWriter, r *http.Request) {
    // Required geographic bounds
    neLat, err := parseFloatParam(r, "neLat", 0)
    if err != nil || neLat == 0 {
        http.Error(w, "neLat parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    neLong, err := parseFloatParam(r, "neLong", 0)
    if err != nil || neLong == 0 {
        http.Error(w, "neLong parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    swLat, err := parseFloatParam(r, "swLat", 0)
    if err != nil || swLat == 0 {
        http.Error(w, "swLat parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    swLong, err := parseFloatParam(r, "swLong", 0)
    if err != nil || swLong == 0 {
        http.Error(w, "swLong parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    // Optional parameters with defaults
    pagination, err := parseIntParam(r, "page", 1)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    zoomValue, err := parseIntParam(r, "zoom", 10)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }
    if zoomValue < 1 || zoomValue > 20 {
        http.Error(w, "zoom parameter must be between 1 and 20", http.StatusBadRequest)
        return
    }

    // Call the search function
    listResults, mapResults, err := search.Sold(
        pagination, zoomValue, neLat, neLong, swLat, swLong,
        nil, // proxyURL
    )

    if err != nil {
        http.Error(w, fmt.Sprintf("Error performing search: %v", err), http.StatusInternalServerError)
        return
    }

    response := SearchResponse{
        ListResults: listResults,
        MapResults:  mapResults,
        TotalCount:  len(listResults),
    }

    writeJSONResponse(w, response)
}
