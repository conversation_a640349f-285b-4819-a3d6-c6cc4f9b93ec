package details

import (
    "encoding/json"
    "fmt"
    "io/ioutil"
    "net/http"
    "net/url"

)

func FromPropertyURL(propertyURL string, proxyURL *url.URL) (PropertyInfo, error) {
    data, err := fromPropertyURL(propertyURL, proxyURL)
    if err != nil {
        return PropertyInfo{}, err
    }
    return data, nil
}

func FromPropertyID(propertyID int64, proxyURL *url.URL) (PropertyInfo, error) {
    propertyURL := fmt.Sprintf("https://www.zillow.com/homedetails/any-title/%d_zpid/", propertyID)
    data, err := fromPropertyURL(propertyURL, proxyURL)
    if err != nil {
        return PropertyInfo{}, err
    }
    return data, nil
}

func FromHomeAddress(address string, proxyURL *url.URL) (PropertyInfo, error) {
    encodedAddress := url.QueryEscape(address)
    apiURL := fmt.Sprintf("https://www.zillowstatic.com/autocomplete/v3/suggestions?q=%s", encodedAddress)

    req, err := http.NewRequest("GET", apiURL, nil)
    if err != nil {
        return PropertyInfo{}, err
    }

    req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

    client := &http.Client{}
    if proxyURL != nil {
        client.Transport = &http.Transport{
            Proxy: http.ProxyURL(proxyURL),
        }
    }

    resp, err := client.Do(req)
    if err != nil {
        return PropertyInfo{}, err
    }
    defer resp.Body.Close()

    if resp.StatusCode != http.StatusOK {
        return PropertyInfo{}, fmt.Errorf("status: %d headers: %v", resp.StatusCode, resp.Header)
    }

    body, err := ioutil.ReadAll(resp.Body)
    if err != nil {
        return PropertyInfo{}, err
    }

    var autocompleteResponse struct {
        Results []struct {
            MetaData struct {
                Zpid int64 `json:"zpid"`
            } `json:"metaData"`
        } `json:"results"`
    }

    if err := json.Unmarshal(body, &autocompleteResponse); err != nil {
        return PropertyInfo{}, err
    }

    if len(autocompleteResponse.Results) == 0 {
        return PropertyInfo{}, fmt.Errorf("no results found for the address")
    }

    zpid := autocompleteResponse.Results[0].MetaData.Zpid
    return FromPropertyID(zpid, proxyURL)
}

// Function to get minimal property information by ID
func FromPropertyIDMinimal(propertyID int64, proxyURL *url.URL) (PropertyMinimalInfo, error) {
    property, err := FromPropertyID(propertyID, proxyURL)
    if err != nil {
        return PropertyMinimalInfo{}, err
    }
    return ToMinimalInfo(property), nil
}

// Function to get minimal property information by URL
func FromPropertyURLMinimal(propertyURL string, proxyURL *url.URL) (PropertyMinimalInfo, error) {
    property, err := FromPropertyURL(propertyURL, proxyURL)
    if err != nil {
        return PropertyMinimalInfo{}, err
    }
    return ToMinimalInfo(property), nil
}

// Function to get minimal property information by address
func FromHomeAddressMinimal(address string, proxyURL *url.URL) (PropertyMinimalInfo, error) {
    property, err := FromHomeAddress(address, proxyURL)
    if err != nil {
        return PropertyMinimalInfo{}, err
    }
    return ToMinimalInfo(property), nil
}

// Function to get only ResponsivePhotos by Property ID
func FromPropertyIDPhotos(propertyID int64, proxyURL *url.URL) (ImagesOnly, error) {
    property, err := FromPropertyID(propertyID, proxyURL)
    if err != nil {
        return ImagesOnly{}, err
    }
    return ToResponsivePhotos(property), nil
}

// Function to get only ResponsivePhotos by Property URL
func FromPropertyURLPhotos(propertyURL string, proxyURL *url.URL) (ImagesOnly, error) {
    property, err := FromPropertyURL(propertyURL, proxyURL)
    if err != nil {
        return ImagesOnly{}, err
    }
    return ToResponsivePhotos(property), nil
}

// Function to get only ResponsivePhotos by Property Address
func FromHomeAddressPhotos(address string, proxyURL *url.URL) (ImagesOnly, error) {
    property, err := FromHomeAddress(address, proxyURL)
    if err != nil {
        return ImagesOnly{}, err
    }
    return ToResponsivePhotos(property), nil
}
