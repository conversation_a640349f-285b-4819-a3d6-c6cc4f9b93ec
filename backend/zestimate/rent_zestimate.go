package zestimate

import (
    "encoding/json"
)

type RentZestimateResponse struct {
    Data RentZestimateData `json:"data"`
}

type RentZestimateData struct {
    ByAddress RentZestimateDetails `json:"byAddress"`
}

type RentZestimateDetails struct {
    Geo              Geo               `json:"geo"`
    Floorplans       []Floorplan       `json:"floorplans"`
    MarketSummary    MarketSummary     `json:"marketSummary"`
    SimilarFloorplans SimilarFloorplans `json:"similarFloorplans"`
}

type Geo struct {
    Lat float64 `json:"lat"`
    Lon float64 `json:"lon"`
}

type Floorplan struct {
    Zpid               int64              `json:"zpid"`
    NumBeds            int                `json:"numBeds"`
    NumFullBaths       IntOrFloat         `json:"numFullBaths"`
    Zestimate          RentZestimateValue `json:"zestimate"`
    MinSqft            int                `json:"minSqft"`
    MaxSqft            int                `json:"maxSqft"`
    Address            Address            `json:"address"`
    PropertyUrl        string             `json:"propertyUrl"`
    RentZestimate      IntOrFloat         `json:"rentZestimate"`
    RentZestimateRange RentZestimateRange `json:"rentZestimateRange"`
}

type RentZestimateValue struct {
    RentZestimate         IntOrFloat `json:"rentZestimate"`
    RentZestimateRangeLow IntOrFloat `json:"rentZestimateRangeLow"`
    RentZestimateRangeHigh IntOrFloat `json:"rentZestimateRangeHigh"`
}

type RentZestimateRange struct {
    Low  IntOrFloat `json:"low"`
    High IntOrFloat `json:"high"`
}

type Address struct {
    Street string `json:"street"`
    City   string `json:"city"`
    State  string `json:"state"`
    Zip    string `json:"zip"`
    Unit   string `json:"unit"`
}

type MarketSummary struct {
    URL      string `json:"url"`
    AreaName string `json:"areaName"`
    Beds     string `json:"beds"`
    Summary  Summary `json:"summary"`
}

type Summary struct {
    MedianRent         IntOrFloat `json:"medianRent"`
    MonthlyChange      IntOrFloat `json:"monthlyChange"`
    YearlyChange       IntOrFloat `json:"yearlyChange"`
    AvgDaysOnMarket    IntOrFloat `json:"avgDaysOnMarket"`
    AvailableRentals   int        `json:"availableRentals"`
}

type SimilarFloorplans struct {
    ListingRanking       int             `json:"listingRanking"`
    HighestPriceIncluded IntOrFloat      `json:"highestPriceIncluded"`
    LowestPriceIncluded  IntOrFloat      `json:"lowestPriceIncluded"`
    BoundingBox          BoundingBox     `json:"boundingBox"`
    Floorplans           []SimilarFloorplan `json:"floorplans"`
}

type BoundingBox struct {
    MinLat float64 `json:"minLat"`
    MaxLat float64 `json:"maxLat"`
    MinLon float64 `json:"minLon"`
    MaxLon float64 `json:"maxLon"`
}

type SimilarFloorplan struct {
    AliasEncoded      string      `json:"aliasEncoded"`
    FloorplanGroupId  string      `json:"floorplanGroupId"`
    Active            bool        `json:"active"`
    Zpid              int64       `json:"zpid"`
    NumBeds           int         `json:"numBeds"`
    NumFullBaths      IntOrFloat  `json:"numFullBaths"`
    NumPartialBaths   int         `json:"numPartialBaths"`
    NumUnitsAvailable int         `json:"numUnitsAvailable"`
    PropertyUrl       string      `json:"propertyUrl"`
    LowPrice          IntOrFloat  `json:"lowPrice"`
    HighPrice         IntOrFloat  `json:"highPrice"`
    DistanceInMiles   float64     `json:"distanceInMiles"`
    MinSqft           int         `json:"minSqft"`
    MaxSqft           int         `json:"maxSqft"`
    Address           Address     `json:"address"`
    Geo               Geo         `json:"geo"`
    Amenities         Amenities   `json:"amenities"`
    History           History     `json:"history"`
    Photos            Photos      `json:"photos"`
}

type Amenities struct {
    Laundry  Laundry  `json:"laundry"`
    Parking  Parking  `json:"parking"`
    Hvac     Hvac     `json:"hvac"`
    Pets     Pets     `json:"pets"`
}

type Laundry struct {
    InUnit  bool `json:"inUnit"`
    Shared  bool `json:"shared"`
}

type Parking struct {
    Valet      bool `json:"valet"`
    Garage     bool `json:"garage"`
    OffStreet  bool `json:"offStreet"`
}

type Hvac struct {
    AirConditioning bool `json:"airConditioning"`
    Heating         bool `json:"heating"`
}

type Pets struct {
    Cats      bool `json:"cats"`
    LargeDogs bool `json:"largeDogs"`
    SmallDogs bool `json:"smallDogs"`
}

type History struct {
    LastUpdated  LastUpdated `json:"lastUpdated"`
    Deactivated  Deactivated `json:"deactivated"`
}

type LastUpdated struct {
    Date  int64  `json:"date"`
    Ago   string `json:"ago"`
}

type Deactivated struct {
    Date  int64  `json:"date"`
    Ago   string `json:"ago"`
}

type Photos struct {
    ListingPhotos []ListingPhoto `json:"listingPhotos"`
}

type ListingPhoto struct {
    Medium string `json:"medium"`
}

type IntOrFloat float64

func (n *IntOrFloat) UnmarshalJSON(data []byte) error {
    var f float64
    if err := json.Unmarshal(data, &f); err != nil {
        return err
    }
    *n = IntOrFloat(f)
    return nil
}
