# PropBolt API

PropBolt is a real estate data API that provides comprehensive property information, rent estimates, and market analysis by interfacing with Zillow's data.

## Quick Start

### Prerequisites
- Go 1.22.3 or later
- Internet connection for data retrieval

### Installation & Running

1. **Clone the repository:**
   ```bash
   <NAME_EMAIL>:brycebayens/rapidapi.git
   cd rapidapi
   ```

2. **Install dependencies:**
   ```bash
   go mod tidy
   ```

3. **Build the application:**
   ```bash
   go build -o propbolt
   ```

4. **Run locally:**
   ```bash
   DISABLE_RAPIDAPI_PROXY_VALIDATION=true DISABLE_FORCE_SSL=true PORT=8080 ./propbolt
   ```
https://www.zillow.com/homes/21057_rid/
5. **Test the API:**
   ```bash
   curl http://localhost:8080/
   # Should return: {"status":"ok"}
   ```

## API Endpoints

### Core Endpoints
- **GET** `/` - Health check
- **GET** `/property` - Full property details
- **GET** `/propertyMinimal` - Essential property information
- **GET** `/propertyImages` - Property images only
- **GET** `/rentEstimate` - Rental market analysis

### Search Endpoints
- **GET** `/search/for-sale` - Search properties for sale
- **GET** `/search/for-rent` - Search rental properties
- **GET** `/search/sold` - Search recently sold properties

### Static Resources
- **GET** `/rapid/tutorial-one-code` - API tutorial
- **GET** `/rapid/curl` - cURL examples
- **GET** `/public/*` - Static files

## Usage Examples

### Get Property by Address
```bash
curl "http://localhost:8080/property?address=123 Main St, City, State"
```

### Get Property by Zillow ID
```bash
curl "http://localhost:8080/property?id=123456789"
```

### Get Rent Estimate
```bash
curl "http://localhost:8080/rentEstimate?address=123 Main St, City, State"
```

### Get Property Images
```bash
curl "http://localhost:8080/propertyImages?id=123456789"
```

## Environment Variables

### Required
- `PORT` - Server port number

### Optional (Development)
- `DISABLE_RAPIDAPI_PROXY_VALIDATION=true` - Disable API key validation
- `DISABLE_FORCE_SSL=true` - Disable SSL enforcement

### Production
- `RAPIDAPI_PROXY_SECRET` - RapidAPI proxy validation secret

## Project Structure

```
├── main.go                 # Main server and HTTP handlers
├── details/               # Property details retrieval
│   ├── data.go           # Data fetching logic
│   ├── formatRaw1.go     # Data structures and parsing
│   ├── get.go            # Public API functions
│   └── parse.go          # HTML parsing utilities
├── zestimate/            # Rent estimation functionality
│   ├── execute.go        # Main execution logic
│   ├── rent_zestimate.go # Data structures
│   └── rent_zestimate_data.go # Data retrieval
├── search/               # Property search capabilities
│   ├── search.go         # Search functions
│   ├── formatInputRaw.go # Request structures
│   ├── formatOutputRaw.go # Response structures
│   └── variables.go      # Constants
├── utils/                # Utility functions
│   └── utils.go          # Helper functions
├── propbolthelper/       # Client helper functions
│   ├── client.go         # Client interface
│   └── format.go         # Data formatting
└── public/               # Static files
    ├── tutorials-one.html
    ├── curl_input.html
    └── assets/
```

## Features

### Property Information
- Complete property details including address, pricing, and specifications
- Property history (price changes, tax records)
- School information and ratings
- Neighborhood data
- High-resolution property images
- Virtual tour links

### Rent Estimation
- Market-based rent estimates
- Comparable property analysis
- Local market trends and statistics
- Rental market summaries by area

### Flexible Data Access
- Multiple input methods (ID, URL, address)
- Configurable response detail levels
- Optional image inclusion
- JSON formatted responses

## Development

### Build Commands
```bash
# Local development build
go build -o propbolt

# Production build (Linux)
GOOS=linux GOARCH=amd64 go build -o propbolt
```

### Testing
```bash
# Run the server
DISABLE_RAPIDAPI_PROXY_VALIDATION=true PORT=8080 ./propbolt

# Test endpoints
curl http://localhost:8080/
curl "http://localhost:8080/property?address=test address"
```

### Dependencies
- `github.com/PuerkitoBio/goquery` - HTML parsing
- `github.com/andybalholm/cascadia` - CSS selectors
- `golang.org/x/net` - Extended networking

## API Documentation

For complete API documentation including all endpoints, parameters, and response formats, see [API_DOCUMENTATION.md](API_DOCUMENTATION.md).

## Production Deployment

### Heroku (Example)
The project includes a `Procfile` for Heroku deployment:
```
web: ./propbolt
```

### Environment Setup
```bash
export PORT=8080
export RAPIDAPI_PROXY_SECRET=your_secret_here
./propbolt
```

## License

This project is proprietary software developed by Byte Media.

## Support

For technical support or questions:
- Email: <EMAIL>
- Check the built-in documentation at `/rapid/tutorial-one-code`

## Version

Current version: june26v1.0 (commit d145dfc)
